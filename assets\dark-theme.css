/* 
 * Homara Dark Mode Color Palette & Typography System
 * Inspired by modern dark mode design principles
 * Consistent styling for all menu pages
 */

/* Import modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

/* CSS Custom Properties - Dark Mode Color Palette */
:root {
  /* Primary Background Colors */
  --bg-primary: #0a0a0a;           /* Deep black background */
  --bg-secondary: #1a1a1a;         /* Slightly lighter black */
  --bg-tertiary: #2a2a2a;          /* Medium dark gray */
  --bg-quaternary: #3a3a3a;        /* Lighter dark gray */
  
  /* Surface Colors */
  --surface-primary: #141414;      /* Card/panel backgrounds */
  --surface-secondary: #1f1f1f;    /* Elevated surfaces */
  --surface-tertiary: #2d2d2d;     /* Interactive surfaces */
  --surface-hover: #383838;        /* Hover states */
  
  /* Border Colors */
  --border-primary: #333333;       /* Main borders */
  --border-secondary: #404040;     /* Secondary borders */
  --border-accent: #555555;        /* Accent borders */
  --border-focus: #666666;         /* Focus states */
  
  /* Text Colors */
  --text-primary: #ffffff;         /* Primary text */
  --text-secondary: #e0e0e0;       /* Secondary text */
  --text-tertiary: #b0b0b0;        /* Tertiary text */
  --text-muted: #808080;           /* Muted text */
  --text-disabled: #606060;        /* Disabled text */
  
  /* Accent Colors */
  --accent-primary: #ffffff;       /* Primary accent (white) */
  --accent-secondary: #f0f0f0;     /* Secondary accent */
  --accent-tertiary: #d0d0d0;      /* Tertiary accent */
  
  /* Interactive Colors */
  --interactive-primary: #ffffff;  /* Primary interactive */
  --interactive-hover: #f5f5f5;    /* Hover state */
  --interactive-active: #e5e5e5;   /* Active state */
  --interactive-disabled: #404040; /* Disabled state */
  
  /* Status Colors */
  --status-success: #4ade80;       /* Success green */
  --status-warning: #fbbf24;       /* Warning yellow */
  --status-error: #f87171;         /* Error red */
  --status-info: #60a5fa;          /* Info blue */
  
  /* Transparency Levels */
  --alpha-10: rgba(255, 255, 255, 0.1);
  --alpha-20: rgba(255, 255, 255, 0.2);
  --alpha-30: rgba(255, 255, 255, 0.3);
  --alpha-40: rgba(255, 255, 255, 0.4);
  --alpha-50: rgba(255, 255, 255, 0.5);
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  --font-display: 'Inter', sans-serif;
  
  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  /* Font Sizes */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  --text-5xl: 3rem;       /* 48px */
  --text-6xl: 3.75rem;    /* 60px */
  
  /* Spacing */
  --space-1: 0.25rem;     /* 4px */
  --space-2: 0.5rem;      /* 8px */
  --space-3: 0.75rem;     /* 12px */
  --space-4: 1rem;        /* 16px */
  --space-5: 1.25rem;     /* 20px */
  --space-6: 1.5rem;      /* 24px */
  --space-8: 2rem;        /* 32px */
  --space-10: 2.5rem;     /* 40px */
  --space-12: 3rem;       /* 48px */
  --space-16: 4rem;       /* 64px */
  --space-20: 5rem;       /* 80px */
  
  /* Border Radius */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  --radius-2xl: 1.5rem;   /* 24px */
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.5);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.25);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-primary);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  min-height: 100vh;
}

/* Typography Classes */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-muted { color: var(--text-muted); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }

/* Layout Components */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6);
}

.surface {
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
}

.surface-elevated {
  background-color: var(--surface-secondary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-md);
}

/* Interactive Elements */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  background-color: var(--surface-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.btn:hover {
  background-color: var(--surface-hover);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background-color: var(--interactive-primary);
  color: var(--bg-primary);
  border-color: var(--interactive-primary);
}

.btn-primary:hover {
  background-color: var(--interactive-hover);
  border-color: var(--interactive-hover);
}

/* Form Elements */
.form-group {
  margin-bottom: var(--space-5);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  font-size: var(--text-sm);
}

.form-input {
  width: 100%;
  padding: var(--space-3);
  background-color: var(--surface-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  transition: all var(--transition-normal);
}

.form-input:focus {
  outline: none;
  border-color: var(--border-focus);
  background-color: var(--surface-tertiary);
  box-shadow: 0 0 0 3px var(--alpha-10);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

/* Utility Classes */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.text-center { text-align: center; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.rounded { border-radius: var(--radius-md); }
.shadow { box-shadow: var(--shadow-md); }

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: var(--space-4);
  }
  
  :root {
    --text-4xl: 2rem;      /* 32px on mobile */
    --text-5xl: 2.5rem;    /* 40px on mobile */
    --text-6xl: 3rem;      /* 48px on mobile */
  }
}
