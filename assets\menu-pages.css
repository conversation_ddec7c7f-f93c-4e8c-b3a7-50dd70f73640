/* 
 * <PERSON><PERSON> Menu Pages - Shared Styling
 * Consistent styling for all menu option pages
 * Uses the dark theme color palette and modern typography
 */

/* Page Layout */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6);
  position: relative;
}

/* Title Canvas Positioning */
.title-canvas {
  position: absolute;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  background-color: transparent;
  z-index: 10;
  pointer-events: none;
  display: block;
}

/* Logo Canvas Positioning - Let JavaScript handle positioning */
#menu-logo-canvas {
  background-color: transparent !important;
  z-index: 1000 !important;
  pointer-events: none !important;
  display: block !important;
}

/* Content Sections */
.content-section {
  margin-bottom: var(--space-16);
  padding: var(--space-8);
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

.content-section h2 {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-bold);
  font-size: var(--text-3xl);
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.content-section h3 {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--text-xl);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.content-section p {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: var(--space-4);
}

.content-section ul {
  margin: var(--space-4) 0;
  padding-left: var(--space-6);
}

.content-section li {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-2);
}

/* Two Column Layout */
.two-column-layout {
  display: flex;
  gap: var(--space-10);
  margin-top: 180px;
  margin-bottom: var(--space-20);
}

.column-left {
  flex: 0 0 33%;
}

.column-right {
  flex: 0 0 65%;
}

/* Registration Demo Box */
.registration-demo {
  background-color: var(--surface-secondary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  backdrop-filter: blur(10px);
}

.registration-demo h2 {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-bold);
  font-size: var(--text-2xl);
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Explanation Text Box */
.explanation-text {
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
}

/* Contact Section */
.contact-section {
  margin-top: var(--space-16);
  padding: var(--space-10);
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
}

.contact-section h3 {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-bold);
  font-size: var(--text-2xl);
  color: var(--text-primary);
  margin-bottom: var(--space-8);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  text-align: center;
}

.contact-form {
  max-width: 600px;
  margin: 0 auto;
}

/* Timeline Specific Styles */
.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-8) 0;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, var(--border-primary), var(--border-secondary));
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--space-12);
  width: 45%;
}

.timeline-item:nth-child(odd) {
  left: 0;
  text-align: right;
  padding-right: var(--space-8);
}

.timeline-item:nth-child(even) {
  left: 55%;
  text-align: left;
  padding-left: var(--space-8);
}

.timeline-item::before {
  content: '';
  position: absolute;
  top: var(--space-6);
  width: 16px;
  height: 16px;
  background-color: var(--accent-primary);
  border: 3px solid var(--bg-primary);
  border-radius: 50%;
  z-index: 1;
}

.timeline-item:nth-child(odd)::before {
  right: -8px;
}

.timeline-item:nth-child(even)::before {
  left: -8px;
}

.timeline-content {
  background-color: var(--surface-secondary);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-secondary);
  backdrop-filter: blur(10px);
}

.timeline-date {
  color: var(--accent-primary);
  font-weight: var(--font-weight-bold);
  font-size: var(--text-lg);
  margin-bottom: var(--space-3);
}

.timeline-title {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-3);
  color: var(--text-primary);
}

.timeline-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-xl);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-bold);
  margin-top: var(--space-3);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-completed {
  background-color: rgba(74, 222, 128, 0.2);
  color: var(--status-success);
  border: 1px solid var(--status-success);
}

.status-current {
  background-color: rgba(251, 191, 36, 0.2);
  color: var(--status-warning);
  border: 1px solid var(--status-warning);
}

.status-planned {
  background-color: var(--alpha-10);
  color: var(--text-tertiary);
  border: 1px solid var(--border-primary);
}

/* Video Container (for Why page) */
.video-container {
  position: absolute;
  top: 25%;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 40%;
  background-color: var(--surface-tertiary);
  border: 3px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  z-index: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-placeholder {
  color: var(--text-tertiary);
  font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);
  text-align: center;
  opacity: 0.7;
}

/* Quote Canvas (for Why page) */
.quote-canvas {
  position: absolute;
  bottom: 10%;
  left: 50%;
  transform: translateX(-50%);
  background-color: transparent;
  z-index: 10;
  pointer-events: none;
  display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
  .two-column-layout {
    flex-direction: column;
    margin-top: 160px;
    gap: var(--space-8);
  }
  
  .column-left,
  .column-right {
    flex: none;
  }
  
  .title-canvas {
    top: 60px;
  }
  
  .timeline::before {
    left: 20px;
  }
  
  .timeline-item {
    width: 100%;
    left: 0 !important;
    padding-left: 50px !important;
    padding-right: 0 !important;
    text-align: left !important;
  }
  
  .timeline-item::before {
    left: 14px !important;
  }
  
  .video-container {
    width: 90%;
    height: 30%;
  }
}
