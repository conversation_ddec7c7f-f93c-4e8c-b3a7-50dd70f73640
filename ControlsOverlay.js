class ControlsOverlay {
    constructor() {
        this.controlsButton = null;
        this.controlsOverlay = null;
        this.isVisible = false;
    }

    init() {
        console.log('ControlsOverlay init() called');
        this.controlsButton = document.getElementById('controls-button');
        this.controlsOverlay = document.getElementById('controls-overlay');
        this.setupEventListeners();
    }

    setupEventListeners() {
        this.controlsButton.addEventListener('click', () => {
            this.toggleControls();
        });

        // Close overlay when clicking outside
        document.addEventListener('click', (event) => {
            if (!this.controlsButton.contains(event.target) && 
                !this.controlsOverlay.contains(event.target)) {
                this.hideControls();
            }
        });
    }

    toggleControls() {
        if (this.isVisible) {
            this.hideControls();
        } else {
            this.showControls();
        }
    }

    showControls() {
        this.controlsOverlay.classList.add('visible');
        this.isVisible = true;
    }

    hideControls() {
        this.controlsOverlay.classList.remove('visible');
        this.isVisible = false;
    }
}
