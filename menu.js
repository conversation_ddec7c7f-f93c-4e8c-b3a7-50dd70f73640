class MenuSystem {
    constructor() {
        this.topMenuTab = null;
        this.sidebarTab = null;
        this.topMenu = null;
        this.sidebar = null;
        this.contentArea = null;
        this.topMenuExpanded = false;
        this.sidebarExpanded = false;
    }

    init() {
        console.log('MenuSystem init() called');
        this.topMenuTab = document.getElementById('top-menu-tab');
        this.sidebarTab = document.getElementById('sidebar-tab');
        this.topMenu = document.getElementById('top-menu');
        this.sidebar = document.getElementById('sidebar');
        this.contentArea = document.getElementById('content-area');
        this.setupEventListeners();
    }

    setupEventListeners() {
        this.topMenuTab.addEventListener('click', () => {
            this.toggleTopMenu();
        });
        this.sidebarTab.addEventListener('click', () => {
            this.toggleSidebar();
        });
    }

    toggleTopMenu() {
        if (this.topMenuExpanded) {
            this.collapseTopMenu();
        } else {
            this.expandTopMenu();
        }
    }

    toggleSidebar() {
        if (this.sidebarExpanded) {
            this.collapseSidebar();
        } else {
            this.expandSidebar();
        }
    }

    expandTopMenu() {
        this.topMenu.classList.add('expanded');
        this.contentArea.classList.add('top-menu-open');
        this.topMenuExpanded = true;
        this.topMenuTab.style.top = '78px'; /* Adjust for taller menu */
        this.topMenuTab.style.right = '200px'; /* Keep further left position */
        this.topMenuTab.textContent = '▲';
    }

    collapseTopMenu() {
        this.topMenu.classList.remove('expanded');
        this.contentArea.classList.remove('top-menu-open');
        this.topMenuExpanded = false;
        this.topMenuTab.style.top = '0px';
        this.topMenuTab.style.right = '200px'; /* Keep further left position */
        this.topMenuTab.textContent = 'MENU';
    }

    expandSidebar() {
        this.sidebar.classList.add('expanded');
        this.contentArea.classList.add('sidebar-open');
        this.sidebarExpanded = true;
        // Position tab at the left edge of the expanded sidebar
        this.sidebarTab.style.right = 'calc(100vw / 6)';
        this.sidebarTab.textContent = '◀';
    }

    collapseSidebar() {
        this.sidebar.classList.remove('expanded');
        this.contentArea.classList.remove('sidebar-open');
        this.sidebarExpanded = false;
        // Reset tab to right edge of screen
        this.sidebarTab.style.right = '0px';
        this.sidebarTab.innerHTML = 'p<br>a<br>s<br>t<br><br>p<br>o<br>i<br>n<br>t<br>s';
    }
}
