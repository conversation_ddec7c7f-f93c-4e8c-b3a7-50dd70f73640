<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>mara</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        @font-face {
            font-family: 'BemboStd';
            src: url('assets/BemboStd-ExtraBold.woff2') format('woff2');
            font-weight: 800;
            font-style: normal;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Hide all scrollbars */
        ::-webkit-scrollbar {
            display: none;
        }

        * {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }

        body {
            font-family: 'BemboStd', 'Inter', Arial, sans-serif;
            background-color: #0f0f0f; /* Even closer to black */
            color: white;
            overflow-x: hidden;
            overflow-y: hidden; /* Hide vertical scrollbar */
            height: 100vh;
        }

        #top-menu {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 80px; /* Increased height for larger logo */
            background-color: #2a2a2a; /* Lighter shade for top menu */
            border-bottom: 2px solid white;
            transform: translateY(-100%);
            transition: transform 0.5s ease-out;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        #top-menu.expanded {
            transform: translateY(0);
        }

        #menu-logo-section {
            display: flex;
            align-items: center;
            flex: 1;
        }

        #menu-logo {
            height: 70px; /* Increased logo size */
            width: auto;
        }

        #community-name {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            font-size: 1.5rem;
            font-weight: 600;
            color: white;
        }

        #top-menu-nav {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        #home-button {
            font-size: 1rem; /* Match other nav links */
            font-weight: bold;
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }

        #home-button:hover {
            background-color: white;
            color: #0f0f0f;
        }

        .top-nav-link {
            color: white;
            text-decoration: none;
            font-size: 1rem;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }

        .top-nav-link:hover {
            background-color: white;
            color: #0f0f0f;
        }

        #top-menu-tab {
            position: fixed;
            top: 0;
            right: 340px; /* Aligned under the join button */
            width: 80px;
            height: 25px;
            background-color: #2a2a2a; /* Match top menu background */
            border: 2px solid white;
            border-top: none; /* Remove top border to connect with menu */
            border-radius: 0 0 8px 8px;
            cursor: pointer;
            z-index: 101;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            color: white;
            transition: all 0.5s ease-out; /* Match menu animation timing */
            opacity: 1;
            user-select: none;
        }

        #top-menu-tab:hover {
            background-color: white;
            color: #2a2a2a;
        }

        #sidebar {
            position: fixed;
            right: 0;
            top: 80px; /* Start right below the menu */
            width: calc(100vw / 6);
            height: calc(100vh - 80px); /* Adjust height to account for menu */
            background-color: #1a1a1a; /* Slightly lighter than main background */
            border-left: 2px solid white;
            border-top: 2px solid white; /* Add top border */
            transform: translateX(100%);
            transition: transform 0.5s ease-out;
            z-index: 99;
            padding: 20px;
            overflow-y: hidden; /* Hide scrollbar */
            box-sizing: border-box; /* Ensure borders don't add to width */
        }

        #sidebar.expanded {
            transform: translateX(0);
        }

        #sidebar-content {
            margin-top: 20px; /* Reduced since sidebar now starts below top menu */
            text-align: center; /* Center the content */
        }

        #sidebar h3 {
            color: white;
            margin-bottom: 20px;
            font-size: 1.1rem;
            border-bottom: 1px solid white;
            padding-bottom: 10px;
            text-align: center; /* Center the "Point History" text */
        }

        #sidebar-tab {
            position: fixed;
            right: 0;
            top: calc(70% + 60px); /* Moved even further down the screen */
            transform: translateY(-50%);
            width: 25px;
            height: 120px; /* Increased height for longer text */
            background-color: #1a1a1a; /* Match sidebar background */
            border: 2px solid white;
            border-right: none;
            border-left: none; /* Remove left border to connect with sidebar */
            border-radius: 8px 0 0 8px;
            cursor: pointer;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem; /* Made text slightly smaller */
            color: white;
            transition: all 0.5s ease-out; /* Match sidebar animation timing */
            opacity: 1;
            user-select: none;
            line-height: 1.1; /* Tighter spacing for better alignment */
            text-align: center; /* Center align the text */
        }

        #sidebar-tab:hover {
            background-color: #1a1a1a; /* Same as sidebar background */
            color: white;
            border-color: #1a1a1a; /* Make border same color on hover */
        }

        #content-area {
            margin-right: 0;
            margin-top: 0;
            padding: 20px;
            transition: margin 0.5s ease-out;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #content-area.sidebar-open {
            margin-right: calc(100vw / 6);
        }

        #content-area.top-menu-open {
            margin-top: 80px; /* Adjust for taller top menu */
        }

        #welcome-message {
            text-align: center;
            font-size: 2rem;
            color: white;
        }

        /* Controls overlay styles */
        #controls-button {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 60px;
            height: 60px;
            background-color: #1a1a1a;
            border: 2px solid white;
            border-radius: 8px;
            cursor: pointer;
            z-index: 102;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            color: white;
            transition: all 0.3s ease;
            user-select: none;
        }

        #controls-button:hover {
            background-color: #2a2a2a;
        }

        #controls-overlay {
            position: fixed;
            bottom: 90px;
            left: 20px;
            background-color: #1a1a1a;
            border: 2px solid white;
            border-radius: 8px;
            padding: 15px;
            z-index: 101;
            color: white;
            font-size: 0.9rem;
            line-height: 1.4;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.3s ease;
            min-width: 200px;
        }

        #controls-overlay.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        #controls-overlay h4 {
            margin-bottom: 10px;
            font-size: 1rem;
            border-bottom: 1px solid white;
            padding-bottom: 5px;
        }

        #controls-overlay .control-item {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div id="top-menu">
        <div id="menu-logo-section">
            <img src="assets/whitelogo.png" alt="Homara Logo" id="menu-logo">
        </div>
        
        <div id="community-name">community name</div>

        <div id="top-menu-nav">
            <a href="join.html" class="top-nav-link">join</a>
            <a href="purpose.html" class="top-nav-link">purpose</a>
            <a href="timeline.html" class="top-nav-link">timeline</a>
            <a href="index.html" id="home-button">home</a>
        </div>
    </div>

    <div id="top-menu-tab">menu</div>

    <div id="sidebar">
        <div id="sidebar-content">
            <h3>point history</h3>
        </div>
    </div>

    <div id="sidebar-tab">h<br>i<br>s<br>t<br>o<br>r<br>y</div>

    <div id="content-area">
        <div id="welcome-message">
            welcome to homara
        </div>
    </div>

    <!-- Controls overlay -->
    <div id="controls-button">ctrl</div>
    <div id="controls-overlay">
        <h4>controls</h4>
        <div class="control-item">wasd - move directions</div>
        <div class="control-item">mouse click and drag - turn</div>
    </div>

    <script src="menu.js"></script>
    <script src="ControlsOverlay.js"></script>
    <script>
        const menuSystem = new MenuSystem();
        menuSystem.init();

        const controlsOverlay = new ControlsOverlay();
        controlsOverlay.init();
    </script>
</body>
</html>
