<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Homara</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', Arial, sans-serif;
            background-color: #2a2a2a;
            color: white;
            overflow-x: hidden;
            height: 100vh;
        }

        #top-menu {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background-color: #2a2a2a;
            border-bottom: 2px solid white;
            transform: translateY(-100%);
            transition: transform 0.5s ease-out;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        #top-menu.expanded {
            transform: translateY(0);
        }

        #menu-logo-section {
            display: flex;
            align-items: center;
            flex: 1;
        }

        #menu-logo {
            height: 50px;
            width: auto;
        }

        #community-name {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            font-size: 1.5rem;
            font-weight: 600;
            color: white;
        }

        #top-menu-nav {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        #home-button {
            font-size: 1.2rem;
            font-weight: bold;
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border: 2px solid white;
            transition: all 0.3s ease;
        }

        #home-button:hover {
            background-color: white;
            color: #2a2a2a;
        }

        .top-nav-link {
            color: white;
            text-decoration: none;
            font-size: 1rem;
            padding: 10px 15px;
            border: 1px solid white;
            transition: all 0.3s ease;
        }

        .top-nav-link:hover {
            background-color: white;
            color: #2a2a2a;
        }

        #top-menu-tab {
            position: fixed;
            top: 0;
            right: 20px;
            width: 80px;
            height: 25px;
            background-color: #2a2a2a;
            border: 2px solid white;
            border-top: none;
            border-radius: 0 0 8px 8px;
            cursor: pointer;
            z-index: 101;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            color: white;
            transition: all 0.3s ease;
            opacity: 1;
            user-select: none;
        }

        #top-menu-tab:hover {
            background-color: white;
            color: #2a2a2a;
        }

        #sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: calc(100vw / 6);
            height: 100vh;
            background-color: #2a2a2a;
            border-left: 2px solid white;
            transform: translateX(100%);
            transition: transform 0.5s ease-out;
            z-index: 99;
            padding: 20px;
        }

        #sidebar.expanded {
            transform: translateX(0);
        }

        #sidebar-content {
            margin-top: 80px;
        }

        #sidebar h3 {
            color: white;
            margin-bottom: 20px;
            font-size: 1.1rem;
            border-bottom: 1px solid white;
            padding-bottom: 10px;
        }

        #sidebar-tab {
            position: fixed;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 25px;
            height: 80px;
            background-color: #2a2a2a;
            border: 2px solid white;
            border-right: none;
            border-radius: 8px 0 0 8px;
            cursor: pointer;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            writing-mode: vertical-rl;
            text-orientation: mixed;
            font-size: 0.8rem;
            color: white;
            transition: all 0.3s ease;
            opacity: 1;
            user-select: none;
        }

        #sidebar-tab:hover {
            background-color: white;
            color: #2a2a2a;
        }

        #content-area {
            margin-right: 0;
            margin-top: 0;
            padding: 20px;
            transition: margin 0.5s ease-out;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #content-area.sidebar-open {
            margin-right: calc(100vw / 6);
        }

        #content-area.top-menu-open {
            margin-top: 60px;
        }

        #welcome-message {
            text-align: center;
            font-size: 2rem;
            color: white;
        }
    </style>
</head>
<body>
    <div id="top-menu">
        <div id="menu-logo-section">
            <img src="assets/whitelogo.png" alt="Homara Logo" id="menu-logo">
        </div>
        
        <div id="community-name">Community Name</div>
        
        <div id="top-menu-nav">
            <a href="join.html" class="top-nav-link">Join</a>
            <a href="purpose.html" class="top-nav-link">Purpose</a>
            <a href="timeline.html" class="top-nav-link">Timeline</a>
            <a href="index.html" id="home-button">HOME</a>
        </div>
    </div>

    <div id="top-menu-tab">MENU</div>

    <div id="sidebar">
        <div id="sidebar-content">
            <h3>History</h3>
        </div>
    </div>

    <div id="sidebar-tab">HISTORY</div>

    <div id="content-area">
        <div id="welcome-message">
            Welcome to Homara
        </div>
    </div>

    <script src="menu.js"></script>
    <script>
        const menuSystem = new MenuSystem();
        menuSystem.init();
    </script>
</body>
</html>
